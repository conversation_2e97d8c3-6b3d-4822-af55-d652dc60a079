// Test the exact case that's failing
import { parsePrice } from './server/src/utils/price-parser.ts';

const testInput = '$1,343.88';
console.log(`Testing exact case: "${testInput}"`);
console.log(`Expected: 1343.88`);
console.log(`Actual: ${parsePrice(testInput)}`);

// Let's also test what the worker would do
console.log('\n--- Testing compiled worker logic ---');

// Simulate the worker's parsePrice function
const CURRENCY_SYMBOLS = ['$', '€', '£', '¥', '₹', '₽', '₩', '₪', '₦', '₡', '₨', '₵', '₴', '₸', '₼', '₾', '₿'];
const CURRENCY_CODES = ['USD', 'EUR', 'GBP', 'JPY', 'INR', 'RUB', 'KRW', 'ILS', 'NGN', 'CRC', 'PKR', 'GHS', 'UAH', 'KZT', 'AZN', 'GEL', 'BTC'];

function workerParsePrice(priceText) {
  if (!priceText || typeof priceText !== 'string') {
    return null;
  }
  let cleanText = priceText.trim().replace(/\s+/g, ' ');
  if (!cleanText) {
    return null;
  }
  const numericPart = workerExtractNumericPart(cleanText);
  if (!numericPart) {
    return null;
  }
  const parsedNumber = workerParseNumericPart(numericPart);
  if (parsedNumber === null || isNaN(parsedNumber) || parsedNumber < 0) {
    return null;
  }
  return parsedNumber.toString();
}

function workerExtractNumericPart(text) {
  let cleaned = text;
  for (const code of CURRENCY_CODES) {
    const codeRegex = new RegExp(`\\b${code}\\b`, 'gi');
    cleaned = cleaned.replace(codeRegex, '').trim();
  }
  for (const symbol of CURRENCY_SYMBOLS) {
    const escapedSymbol = symbol.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const symbolRegex = new RegExp(escapedSymbol, 'g');
    cleaned = cleaned.replace(symbolRegex, '').trim();
  }
  const numericMatch = cleaned.match(/[\d\s,.']+/);
  if (!numericMatch) {
    return null;
  }
  return numericMatch[0].trim();
}

function workerParseNumericPart(numericText) {
  if (!numericText) {
    return null;
  }
  const patterns = [
    {
      regex: /^([\d\s.']+),(\d{1,2})$/,
      parse: (match) => {
        const integerPart = match[1].replace(/[\s.']/g, '');
        const decimalPart = match[2];
        return parseFloat(`${integerPart}.${decimalPart}`);
      }
    },
    {
      regex: /^([\d\s,']*)\.(\d{1,3})$/,
      parse: (match) => {
        const integerPart = match[1].replace(/[\s,']/g, '');
        const decimalPart = match[2];
        return parseFloat(`${integerPart}.${decimalPart}`);
      }
    },
    {
      regex: /^[\d\s,.']+$/,
      parse: (match) => {
        const fullMatch = match[0];
        if (/^\d+$/.test(fullMatch)) {
          return parseFloat(fullMatch);
        }
        const parts = fullMatch.split(/[\s,.']/);
        const lastPart = parts[parts.length - 1];
        if (parts.length === 2 && lastPart.length <= 2) {
          const integerPart = parts[0];
          const decimalPart = lastPart;
          return parseFloat(`${integerPart}.${decimalPart}`);
        } else {
          const cleaned = fullMatch.replace(/[\s,.']/g, '');
          return parseFloat(cleaned);
        }
      }
    }
  ];

  for (const pattern of patterns) {
    const match = numericText.match(pattern.regex);
    if (match) {
      try {
        const result = pattern.parse(match);
        if (!isNaN(result) && result >= 0) {
          console.log(`Matched pattern: ${pattern.regex}`);
          console.log(`Match groups:`, match);
          console.log(`Result: ${result}`);
          return result;
        }
      } catch (error) {
        continue;
      }
    }
  }
  return null;
}

console.log(`Worker result: ${workerParsePrice(testInput)}`);

// Let's trace through step by step
console.log('\n--- Step by step trace ---');
const cleanText = testInput.trim().replace(/\s+/g, ' ');
console.log(`1. Clean text: "${cleanText}"`);

const numericPart = workerExtractNumericPart(cleanText);
console.log(`2. Numeric part: "${numericPart}"`);

console.log(`3. Testing patterns on "${numericPart}":`);
const pattern2 = /^([\d\s,']*)\.(\d{1,3})$/;
const match2 = numericPart.match(pattern2);
console.log(`   Pattern 2 (dot decimal): ${pattern2} -> `, match2);

if (match2) {
  const integerPart = match2[1].replace(/[\s,']/g, '');
  const decimalPart = match2[2];
  console.log(`   Integer part: "${match2[1]}" -> cleaned: "${integerPart}"`);
  console.log(`   Decimal part: "${decimalPart}"`);
  console.log(`   Final: ${integerPart}.${decimalPart} = ${parseFloat(`${integerPart}.${decimalPart}`)}`);
}
