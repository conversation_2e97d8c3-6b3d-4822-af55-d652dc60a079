import { gotScraping } from "got-scraping";
import * as cheerio from "cheerio";
import type { ScraperAdapter, ScrapeResult } from "./types";

function extractPrice(priceText: string): string | null {
  const priceMatch = priceText.match(/[\d,]+\.?\d*/);
  if (!priceMatch) return null;
  
  const price = priceMatch[0].replace(/,/g, '');
  const numericPrice = parseFloat(price);
  
  return isNaN(numericPrice) ? null : numericPrice.toString();
}

export const gotScrapingAdapter: ScraperAdapter = {
  name: 'gotScraping',
  async scrape(url: string, selector: string): Promise<ScrapeResult> {
    try {
      // Use got-scraping with minimal configuration to leverage its anti-detection features
      const response = await gotScraping.get(url, {
        timeout: { request: 15000 } // Only increase timeout, let got-scraping handle the rest
      });

      const html = response.body;
      const $ = cheerio.load(html);

      const priceElement = $(selector);
      if (priceElement.length === 0) {
        // Return null price but no error - this allows fallback to Playwright
        return { price: null };
      }

      const priceText = priceElement.first().text().trim();
      const price = extractPrice(priceText);

      if (!price) {
        return { price: null, error: `Could not extract a valid price from text: "${priceText}"` };
      }

      return { price };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      console.log(`[GOT-SCRAPING] Failed for ${url}: ${errorMessage}`);
      return { price: null, error: errorMessage };
    }
  }
};
